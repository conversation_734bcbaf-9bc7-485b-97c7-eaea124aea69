import React, { useState, memo, useCallback, useMemo } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';

interface SliderItem {
  id: number;
  title: string;
  description: string;
  imageUrl: string;
}

const ImageSlider: React.FC = memo(() => {
  const initialItems: SliderItem[] = useMemo(() => [
    {
      id: 1,
      title: "Discover Tanzania",
      description: "Embark on an extraordinary journey through Tanzania's diverse landscapes, from vast savannas to pristine coastlines, discovering the heart of East Africa.",
      imageUrl: "https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fzanziabr.jpg?alt=media&token=d3cb3c68-f330-47d3-92b3-450285b371ba"
    },
    {
      id: 2,
      title: "Serengeti National Park",
      description: "Witness the greatest wildlife spectacle on Earth in the endless plains of Serengeti, home to the Great Migration and abundant wildlife.",
      imageUrl: "https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fserengeti.jpg?alt=media&token=d0cc83f1-d6c5-4176-a427-aa3e515292c2"
    },
    {
      id: 3,
      title: "Ngorongoro Crater",
      description: "Explore the world's largest intact volcanic caldera, a natural amphitheater teeming with wildlife in this UNESCO World Heritage Site.",
      imageUrl: "https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fngorngoro.jpg?alt=media&token=99b6f80c-406e-4502-9297-4d06541fdea2"
    },
    {
      id: 4,
      title: "Mount Kilimanjaro",
      description: "Conquer Africa's highest peak and stand atop the roof of Africa, experiencing diverse ecosystems from rainforest to arctic summit.",
      imageUrl: "https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fkilimanjaro.jpg?alt=media&token=78d428c9-b26f-43b6-ae10-a341b9c54a30"
    },
    {
      id: 5,
      title: "Zanzibar Island",
      description: "Relax on pristine white sand beaches and explore the historic Stone Town in this tropical island paradise off Tanzania's coast.",
      imageUrl: "https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fzanzi.jpg?alt=media&token=c408bdff-4daa-43ea-963c-f790ac1b33de"
    }
  ], []);

  const [items, setItems] = useState<SliderItem[]>(initialItems);

  const handleNext = useCallback(() => {
    setItems(prev => {
      const newItems = [...prev];
      const firstItem = newItems.shift()!;
      newItems.push(firstItem);
      return newItems;
    });
  }, []);

  const handlePrev = useCallback(() => {
    setItems(prev => {
      const newItems = [...prev];
      const lastItem = newItems.pop()!;
      newItems.unshift(lastItem);
      return newItems;
    });
  }, []);

  // Inline styles object to replace styled-jsx
  const sliderStyles = {
    container: {
      position: 'relative' as const,
      width: '100%',
      height: '100vh',
      overflow: 'hidden'
    }
  };

  return (
    <div style={sliderStyles.container} className="relative w-full h-screen overflow-hidden" aria-label="Safari experiences slider">
      <style>{`
        .slider-item {
          width: 200px;
          height: 300px;
          list-style-type: none;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          z-index: 1;
          background-position: center;
          background-size: cover;
          border-radius: 2px;
          box-shadow: 0 20px 30px rgba(212, 194, 164, 0.3) inset;
          transition: transform 0.1s, left 0.75s, top 0.75s, width 0.75s, height 0.75s;
        }

        .slider-item:nth-child(1),
        .slider-item:nth-child(2) {
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          transform: none;
          border-radius: 0;
          box-shadow: none;
          opacity: 1;
        }

        .slider-item:nth-child(3) {
          left: 50%;
        }

        .slider-item:nth-child(4) {
          left: calc(50% + 220px);
        }

        .slider-item:nth-child(5) {
          left: calc(50% + 440px);
        }

        .slider-item:nth-child(6) {
          left: calc(50% + 660px);
          opacity: 0;
        }

        .slider-content {
          width: min(30vw, 400px);
          position: absolute;
          top: 50%;
          left: 3rem;
          transform: translateY(-50%);
          font-family: sans-serif;
          color: #3C352D;
          text-shadow: none;
          opacity: 0;
          display: none;
        }

        .slider-content .title {
          font-family: 'Cormorant Garamond', serif;
          font-weight: 400;
          font-size: 2.5rem;
          text-transform: none;
          color: #3C352D;
          margin-bottom: 1rem;
          line-height: 1.2;
        }

        .slider-content .description {
          line-height: 1.6;
          margin: 1rem 0 1.5rem;
          font-size: 1rem;
          color: #4B4237;
          font-family: sans-serif;
        }

        .slider-content button {
          width: fit-content;
          background-color: #B95E2D;
          color: #F5F1EB;
          border: none;
          border-radius: 0;
          padding: 1rem 2rem;
          cursor: pointer;
          font-family: sans-serif;
          font-weight: 700;
          font-size: 0.75rem;
          letter-spacing: 0.15em;
          text-transform: uppercase;
          transition: background-color 0.3s ease;
        }

        .slider-content button:hover {
          background-color: #a05126;
          color: #F5F1EB;
        }

        .slider-item:nth-of-type(2) .slider-content {
          display: block;
          animation: show 0.75s ease-in-out 0.3s forwards;
        }

        @keyframes show {
          0% {
            filter: blur(5px);
            transform: translateY(calc(-50% + 75px));
          }
          100% {
            opacity: 1;
            filter: blur(0);
          }
        }

        .nav {
          position: absolute;
          bottom: 2rem;
          left: 50%;
          transform: translateX(-50%);
          z-index: 5;
          user-select: none;
        }

        .nav .btn {
          background-color: rgba(212, 194, 164, 0.2);
          color: #D4C2A4;
          border: 2px solid rgba(212, 194, 164, 0.6);
          margin: 0 0.25rem;
          padding: 0.75rem;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .nav .btn:hover {
          background-color: rgba(212, 194, 164, 0.4);
          border-color: #D4C2A4;
        }

        /* Enhanced Mobile Responsiveness */
        @media (max-width: 1024px) {
          .slider-content {
            width: min(40vw, 350px);
            left: 2rem;
          }
          .slider-content .title {
            font-size: 2.2rem;
          }
          .slider-content .description {
            font-size: 0.95rem;
          }
        }

        @media (max-width: 768px) {
          .slider-content {
            width: min(50vw, 300px);
            left: 1.5rem;
          }
          .slider-content .title {
            font-size: 1.8rem;
            margin-bottom: 0.75rem;
          }
          .slider-content .description {
            font-size: 0.85rem;
            margin: 0.75rem 0 1rem;
          }
          .slider-content button {
            font-size: 0.85rem;
            padding: 0.6rem 1.2rem;
          }

          .slider-item {
            width: 160px;
            height: 270px;
          }

          .slider-item:nth-child(3) { left: 50%; }
          .slider-item:nth-child(4) { left: calc(50% + 170px); }
          .slider-item:nth-child(5) { left: calc(50% + 340px); }
          .slider-item:nth-child(6) { left: calc(50% + 510px); opacity: 0; }
        }

        @media (max-width: 640px) {
          .slider-content {
            width: min(60vw, 250px);
            left: 1rem;
          }
          .slider-content .title {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
          }
          .slider-content .description {
            font-size: 0.8rem;
            margin: 0.5rem 0 0.75rem;
            line-height: 1.5;
          }
          .slider-content button {
            font-size: 0.8rem;
            padding: 0.5rem 1rem;
          }

          .slider-item {
            width: 130px;
            height: 220px;
          }

          .slider-item:nth-child(3) { left: 50%; }
          .slider-item:nth-child(4) { left: calc(50% + 140px); }
          .slider-item:nth-child(5) { left: calc(50% + 280px); }
          .slider-item:nth-child(6) { left: calc(50% + 420px); opacity: 0; }

          .nav {
            bottom: 1.5rem;
          }

          .nav .btn {
            padding: 0.6rem;
            margin: 0 0.2rem;
          }
        }

        @media (max-width: 480px) {
          .slider-content {
            width: min(70vw, 200px);
            left: 0.75rem;
          }
          .slider-content .title {
            font-size: 1.25rem;
            margin-bottom: 0.4rem;
          }
          .slider-content .description {
            font-size: 0.75rem;
            margin: 0.4rem 0 0.6rem;
            line-height: 1.4;
          }
          .slider-content button {
            font-size: 0.75rem;
            padding: 0.4rem 0.8rem;
          }

          .slider-item {
            width: 110px;
            height: 180px;
          }

          .slider-item:nth-child(3) { left: 50%; }
          .slider-item:nth-child(4) { left: calc(50% + 120px); }
          .slider-item:nth-child(5) { left: calc(50% + 240px); }
          .slider-item:nth-child(6) { left: calc(50% + 360px); opacity: 0; }

          .nav {
            bottom: 1rem;
          }

          .nav .btn {
            padding: 0.5rem;
            margin: 0 0.15rem;
          }
        }

        @media (max-width: 360px) {
          .slider-content {
            width: min(75vw, 180px);
            left: 0.5rem;
          }
          .slider-content .title {
            font-size: 1.1rem;
            margin-bottom: 0.3rem;
          }
          .slider-content .description {
            font-size: 0.7rem;
            margin: 0.3rem 0 0.5rem;
            line-height: 1.3;
          }
          .slider-content button {
            font-size: 0.7rem;
            padding: 0.35rem 0.7rem;
          }

          .slider-item {
            width: 100px;
            height: 160px;
          }

          .slider-item:nth-child(3) { left: 50%; }
          .slider-item:nth-child(4) { left: calc(50% + 110px); }
          .slider-item:nth-child(5) { left: calc(50% + 220px); }
          .slider-item:nth-child(6) { left: calc(50% + 330px); opacity: 0; }
        }
      `}</style>
      
      <div className="h-full w-full grid place-items-center">
        <main className="relative w-full h-full">
          <ul className="slider">
            {items.map((item) => (
              <li
                key={item.id}
                className="slider-item"
                style={{ backgroundImage: `url('${item.imageUrl}')` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/20 to-transparent" />
                <div className="slider-content">
                  <h2 className="title">{item.title}</h2>
                  <p className="description">{item.description}</p>
                  <Link to="/destinations">
                    <button>Explore More</button>
                  </Link>
                </div>
              </li>
            ))}
          </ul>
          
          <nav className="nav">
            <button className="btn prev" onClick={handlePrev} aria-label="Previous slide">
              <ChevronLeft size={20} />
            </button>
            <button className="btn next" onClick={handleNext} aria-label="Next slide">
              <ChevronRight size={20} />
            </button>
          </nav>
        </main>
      </div>
    </div>
  );
});

ImageSlider.displayName = 'ImageSlider';

export default ImageSlider;