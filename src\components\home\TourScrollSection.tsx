import React, { useRef, useEffect, useState } from 'react';

const TourScrollSection = () => {
  const cardsPanelRef = useRef<HTMLDivElement>(null);
  const cardsWrapperRef = useRef<HTMLDivElement>(null);
  const [isMobile, setIsMobile] = useState(false);

  // Safari data
  const safaris = [
    {
      image: "https://images.unsplash.com/photo-1557050543-4d5f4e07d7c2?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800",
      alt: "Two large elephants in the Serengeti with a hot air balloon in the background",
      duration: "7 DAYS",
      title: "Serengeti Safari Adventure",
      description: "Delve into the safari that takes you through Tanzania's most famous national parks, offering unparalleled wildlife viewing, including..."
    },
    {
      image: "https://images.unsplash.com/photo-1549488344-cbb6c34cf08b?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800",
      alt: "A lioness resting on a thick tree branch",
      duration: "5 DAYS",
      title: "Ngorongoro Crater and Lake Manyara...",
      description: "A shorter safari focusing on two of Tanzania's most unique ecosystems, ideal for travelers with limited time but a desire for..."
    },
    {
      image: "https://images.unsplash.com/photo-1589395937658-0557e7d89fad?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800",
      alt: "Three giraffes standing in a field with Mount Kilimanjaro in the background",
      duration: "7 - 10 DAYS",
      title: "Mount Kilimanjaro Trek",
      description: "A challenging yet rewarding adventure to summit Africa's highest peak, offering breathtaking views and a sense of accomplishme..."
    },
    {
      image: "https://images.unsplash.com/photo-1531332247349-3e5f0538fb69?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800",
      alt: "Maasai tribe members in traditional red clothing dancing",
      duration: "3 DAYS",
      title: "Maasai Cultural Experience",
      description: "Immerse yourself in the vibrant culture of the Maasai people, learning about their ancient traditions, ceremonies, and daily life in the..."
    },
    {
      image: "https://images.unsplash.com/photo-1601758177266-bc5a588b6a24?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800",
      alt: "A tranquil beach in Zanzibar with a traditional dhow boat",
      duration: "6 DAYS",
      title: "Zanzibar's Spice Islands",
      description: "Relax on pristine white-sand beaches, explore historic Stone Town, and discover the aromatic spice farms of this exotic archipelago..."
    }
  ];

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 900);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (isMobile || !cardsPanelRef.current || !cardsWrapperRef.current) return;

    const cardsPanel = cardsPanelRef.current;
    const cardsWrapper = cardsWrapperRef.current;

    let state = {
      isDragging: false,
      startY: 0,
      startTranslate: 0,
      currentTranslate: 0,
      lastTranslate: 0,
      velocity: 0,
      animationFrame: 0,
    };

    const getPositionY = (e: MouseEvent | TouchEvent) => 
      e.type.includes('mouse') ? (e as MouseEvent).pageY : (e as TouchEvent).touches[0].clientY;

    function dragStart(e: MouseEvent | TouchEvent) {
      if(e.type === 'touchstart') e.preventDefault();

      state.isDragging = true;
      state.startY = getPositionY(e);
      state.startTranslate = state.currentTranslate;
      state.velocity = 0;
      
      cardsPanel.classList.add('grabbing');
      
      cancelAnimationFrame(state.animationFrame);
      state.animationFrame = requestAnimationFrame(animationLoop);
    }

    function drag(e: MouseEvent | TouchEvent) {
      if (!state.isDragging) return;
      const currentY = getPositionY(e);
      const diff = currentY - state.startY;
      state.currentTranslate = state.startTranslate + diff;
    }

    function dragEnd() {
      if (!state.isDragging) return;
      state.isDragging = false;
      cardsPanel.classList.remove('grabbing');
      
      cancelAnimationFrame(state.animationFrame);
      state.animationFrame = requestAnimationFrame(momentumLoop);
    }
    
    function animationLoop() {
      if (!state.isDragging) return;
      
      const moved = state.currentTranslate - state.lastTranslate;
      state.velocity = moved * 5;
      state.lastTranslate = state.currentTranslate;

      setTranslate(false);
      state.animationFrame = requestAnimationFrame(animationLoop);
    }

    function momentumLoop() {
      state.currentTranslate += state.velocity;
      state.velocity *= 0.94;

      if (setTranslate(true) || Math.abs(state.velocity) < 0.1) {
        cancelAnimationFrame(state.animationFrame);
        return;
      }
      
      state.animationFrame = requestAnimationFrame(momentumLoop);
    }
    
    function setTranslate(snapToBounds: boolean) {
      const panelHeight = cardsPanel.offsetHeight;
      const wrapperHeight = cardsWrapper.scrollHeight;
      const maxTranslate = 0;
      const minTranslate = panelHeight - wrapperHeight < 0 ? panelHeight - wrapperHeight : 0;
      
      let boundaryHit = false;

      if (snapToBounds) {
        if (state.currentTranslate > maxTranslate) {
          state.currentTranslate = maxTranslate;
          boundaryHit = true;
        } else if (state.currentTranslate < minTranslate) {
          state.currentTranslate = minTranslate;
          boundaryHit = true;
        }
      } else {
        if (state.currentTranslate > maxTranslate) {
          state.currentTranslate = maxTranslate + (state.currentTranslate - maxTranslate) * 0.4;
        } else if (state.currentTranslate < minTranslate) {
          state.currentTranslate = minTranslate + (state.currentTranslate - minTranslate) * 0.4;
        }
      }

      cardsWrapper.style.transform = `translateY(${state.currentTranslate}px)`;
      return boundaryHit;
    }

    // Event listeners
    cardsPanel.addEventListener('mousedown', dragStart as EventListener);
    window.addEventListener('mousemove', drag as EventListener);
    window.addEventListener('mouseup', dragEnd);
    
    cardsPanel.addEventListener('touchstart', dragStart as EventListener, { passive: false });
    window.addEventListener('touchmove', drag as EventListener, { passive: false });
    window.addEventListener('touchend', dragEnd);
    
    // Prevent default dragging
    const preventDrag = (e: Event) => e.preventDefault();
    cardsWrapper.querySelectorAll('img, a').forEach(el => {
      el.addEventListener('dragstart', preventDrag);
    });

    return () => {
      cardsPanel.removeEventListener('mousedown', dragStart as EventListener);
      window.removeEventListener('mousemove', drag as EventListener);
      window.removeEventListener('mouseup', dragEnd);
      cardsPanel.removeEventListener('touchstart', dragStart as EventListener);
      window.removeEventListener('touchmove', drag as EventListener);
      window.removeEventListener('touchend', dragEnd);
      cardsWrapper.querySelectorAll('img, a').forEach(el => {
        el.removeEventListener('dragstart', preventDrag);
      });
      cancelAnimationFrame(state.animationFrame);
    };
  }, [isMobile]);

  return (
    <div className="relative w-full bg-[#F5F1EB] overflow-hidden">
      {/* Section Height: Match other sections with responsive heights */}
      <div 
        className="w-full"
        style={{
          height: isMobile ? 'auto' : '100vh',
          minHeight: isMobile ? '600px' : '100vh'
        }}
      >
        {/* Main Container: Grid layout like the original */}
        <div 
          className={`
            w-full h-full
            ${isMobile ? 'flex flex-col' : 'grid grid-cols-[45%_55%]'}
          `}
        >
          {/* Left Intro Panel */}
          <div 
            className={`
              flex justify-center items-center
              ${isMobile ? 'text-center py-16 px-8' : 'px-[5vw]'}
            `}
          >
            <div className={`${isMobile ? 'w-full' : 'max-w-[450px]'}`}>
              <span 
                className="block font-['Montserrat',sans-serif] text-xs tracking-[0.2em] font-bold text-[#4B4237] opacity-80 uppercase mb-4"
              >
                Unforgettable Journeys
              </span>
              <h1
                className="font-['Cormorant_Garamond'] text-[clamp(2.5rem,4vw,3.2rem)] leading-[1.2] my-6 text-[#3C352D] font-normal"
              >
                Our Classic Safaris,<br/>
                Timeless Journeys,<br/>
                <em className="opacity-70">Masterfully Curated.</em>
              </h1>
              <p 
                className={`
                  text-sm mb-8 leading-relaxed text-[#4B4237]
                  ${isMobile ? 'mx-auto' : ''}
                  max-w-[40ch]
                `}
              >
                Imagine Africa, perfectly planned. Our expert team meticulously crafts classic safari tours, 
                ensuring every detail is seamless. These tried and tested itineraries mean you simply enjoy 
                a luxurious, worry-free adventure.
              </p>
              <button 
                className="inline-block bg-[#B95E2D] text-[#F5F1EB] font-['Montserrat',sans-serif] font-bold text-xs tracking-[0.15em] py-4 px-8 uppercase transition-colors duration-300 hover:bg-[#a05126] border-none cursor-pointer"
              >
                Explore More
              </button>
            </div>
          </div>

          {/* Right Cards Panel */}
          <div 
            ref={cardsPanelRef}
            className={`
              ${isMobile ? 'h-auto overflow-visible cursor-default select-auto' : 'h-full overflow-hidden cursor-grab select-none'}
              ${!isMobile ? 'hover:cursor-grab active:cursor-grabbing' : ''}
            `}
            style={{
              userSelect: isMobile ? 'auto' : 'none'
            }}
          >
            <div 
              ref={cardsWrapperRef}
              className="will-change-transform"
              style={{
                padding: isMobile ? '0 0 1rem 0' : '2.5rem 0',
                transform: isMobile ? 'none' : undefined
              }}
            >
              {safaris.map((safari, index) => (
                <div 
                  key={index} 
                  className="w-4/5 max-w-[400px] mx-auto mb-8 shadow-[0_10px_30px_rgba(0,0,0,0.05)] overflow-hidden rounded"
                >
                  <img 
                    src={safari.image} 
                    alt={safari.alt}
                    className="w-full h-[250px] object-cover block"
                    draggable={false}
                  />
                  <div className="bg-[#EAE3D6] p-6">
                    <span className="font-['Montserrat',sans-serif] text-[0.65rem] tracking-[0.15em] font-bold text-[#4B4237] opacity-80 block mb-2 uppercase">
                      {safari.duration}
                    </span>
                    <h2 className="font-['Playfair_Display',serif] text-2xl leading-[1.3] text-[#3C352D] mb-2 font-normal">
                      {safari.title}
                    </h2>
                    <p className="font-['Montserrat',sans-serif] text-[0.85rem] text-[#4B4237] leading-relaxed">
                      {safari.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Custom CSS for grabbing state */}
      <style jsx>{`
        .grabbing {
          cursor: grabbing !important;
        }
        
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Playfair+Display:wght@400;700&display=swap');
      `}</style>
    </div>
  );
};

export default TourScrollSection;
